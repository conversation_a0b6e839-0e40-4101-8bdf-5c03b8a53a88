import api from "@/libs/request";

/**
 * SSE连接相关API
 */

/**
 * 建立消息通知SSE连接
 * @param clientId 客户端ID
 */
export function connectMessageSSE(clientId: string) {
  // 返回SSE连接
  const token = localStorage.getItem('token') || '';
  const baseUrl = process.env.VUE_APP_BASEURL || '';
  const url = `${baseUrl}/platform-open/sse/connect?loginType=PC`;

  return {
    url,
    headers: {
      'Token': token.slice(1, -1),
      'Accept': 'text/event-stream',
      'Cache-Control': 'no-cache'
    }
  };
}