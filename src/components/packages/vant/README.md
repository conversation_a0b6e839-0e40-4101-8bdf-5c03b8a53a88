<p align="center">
    <img alt="logo" src="https://img.yzcdn.cn/vant/logo.png" width="120" height="120" style="margin-bottom: 10px;">
</p>

<h3 align="center" style="margin: 30px 0 35px;">Mobile UI Components built on Vue</h3>

<p align="center">
    <img src="https://img.shields.io/npm/v/vant.svg?style=for-the-badge" alt="npm version" />
    <img src="https://img.shields.io/github/workflow/status/youzan/vant/CI/dev?style=for-the-badge" alt="npm version" />
    <img src="https://img.shields.io/codecov/c/github/youzan/vant/dev.svg?style=for-the-badge&color=#4fc08d" alt="Coverage Status" />
    <img src="https://img.shields.io/npm/dm/vant.svg?style=for-the-badge&color=#4fc08d" alt="downloads" />
    <img src="https://img.badgesize.io/https://unpkg.com/vant/lib/vant.min.js?compression=gzip&style=for-the-badge&label=gzip%20size&color=#4fc08d" alt="Gzip Size" />
</p>

<p align="center">
  🔥 <a href="https://youzan.github.io/vant">文档网站</a>
  &nbsp;
  &nbsp;
  💡 <a href="https://vant-contrib.gitee.io/vant">国内镜像文档</a>
  &nbsp;
  &nbsp;
  🇨🇳 <a href="./README.zh-CN.md">中文版介绍</a>
  &nbsp;
  &nbsp;
  🚀 <a href="https://github.com/youzan/vant-weapp" target="_blank">小程序版</a>
</p>

---

## Features

- 60+ Reusable components
- 90% Unit test coverage
- Extensive documentation and demos
- Support [babel-plugin-import](https://github.com/ant-design/babel-plugin-import)
- Support Custom Theme
- Support i18n
- Support TS
- Support SSR

## Install

```bash
# Using npm
npm i vant -S

# Using yarn
yarn add vant
```

## Quickstart

```js
import Vue from 'vue';
import { Button } from 'vant';
import 'vant/lib/index.css';

Vue.use(Button);
```

See more in [Quickstart](https://youzan.github.io/vant#/en-US/quickstart).

## Contribution

Please make sure to read the [Contributing Guide](./.github/CONTRIBUTING.md) before making a pull request.

## Browser Support

Modern browsers and Android 4.0+, iOS 8.0+.

## Ecosystem

| Project | Description |
| --- | --- |
| [vant-demo](https://github.com/youzan/vant-demo) | Official vant demo collection |
| [vant-weapp](https://github.com/youzan/vant-weapp) | WeChat MiniProgram UI |
| [vant-cli](https://github.com/youzan/vant/tree/dev/packages/vant-cli) | Scaffold for UI library |
| [vant-icons](https://github.com/youzan/vant/tree/dev/packages/vant-icons) | Vant icons |
| [vant-touch-emulator](https://github.com/youzan/vant/tree/dev/packages/vant-touch-emulator) | Using vant in desktop browsers |

## Links

- [Documentation](https://youzan.github.io/vant)
- [Changelog](https://youzan.github.io/vant#/en-US/changelog)

## Preview

You can scan the following QR code to access the demo：

<img src="https://img.yzcdn.cn/vant/preview_qrcode_20180528.png" width="220" height="220" >

## Wechat Group

Scan the qrcode to join our wechat discussion group, please note that you want to join Vant discussion group.

<img src="https://img.yzcdn.cn/vant/wechat_20180606.png" width="220" height="292" >

## LICENSE

[MIT](https://en.wikipedia.org/wiki/MIT_License)
