{"_from": "vant@^2.4.7", "_id": "vant@2.6.2", "_inBundle": false, "_integrity": "sha1-1WM0IrS4LYrko3OFKS6FcoJTgns=", "_location": "/vant", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "vant@^2.4.7", "name": "vant", "escapedName": "vant", "rawSpec": "^2.4.7", "saveSpec": null, "fetchSpec": "^2.4.7"}, "_requiredBy": ["/"], "_resolved": "https://registry.npm.taobao.org/vant/download/vant-2.6.2.tgz", "_shasum": "d5633422b4b82d8ae4a37385292e85728253827b", "_spec": "vant@^2.4.7", "_where": "/Users/<USER>/Project/self/group-mall/group-mall-admin", "author": {"name": "<PERSON><PERSON><PERSON>"}, "browserslist": ["Android >= 4.0", "iOS >= 8"], "bugs": {"url": "https://github.com/youzan/vant/issues"}, "bundleDependencies": false, "dependencies": {"@babel/runtime": "7.x", "@vant/icons": "1.2.1", "@vue/babel-helper-vue-jsx-merge-props": "^1.0.0", "vue-lazyload": "1.2.3"}, "deprecated": false, "description": "Mobile UI Components built on Vue", "devDependencies": {"@ls-lint/ls-lint": "^1.8.0", "@vant/cli": "^2.4.0", "prettier": "^2.0.4", "vue": "^2.6.11", "vue-template-compiler": "^2.6.11"}, "eslintConfig": {"root": true, "extends": ["@vant"]}, "files": ["es", "lib", "types", "vetur"], "homepage": "https://github.com/youzan/vant#readme", "husky": {"hooks": {"pre-commit": "lint-staged && ls-lint", "commit-msg": "vant-cli commit-lint"}}, "keywords": ["vue", "component"], "license": "MIT", "lint-staged": {"*.md": ["prettier --write"], "*.{ts,tsx,js,vue,less}": ["prettier --write", "git add"], "*.{ts,tsx,js,vue}": ["eslint --fix", "git add"], "*.{vue,css,less}": ["stylelint --fix", "git add"]}, "main": "lib/index.js", "module": "es/index.js", "name": "vant", "peerDependencies": {"vue": ">= 2.5.22"}, "prettier": {"singleQuote": true, "proseWrap": "never"}, "repository": {"type": "git", "url": "git+ssh://**************/youzan/vant.git"}, "scripts": {"bootstrap": "yarn || npm i", "build": "vant-cli build", "dev": "vant-cli dev", "lint": "vant-cli lint && ls-lint", "release": "vant-cli release", "release:site": "sh docs/site/release.sh", "test": "vant-cli test", "test:coverage": "open test/coverage/index.html", "test:watch": "vant-cli test --watch"}, "sideEffects": ["es/**/style/*", "lib/**/style/*", "*.css", "*.less"], "style": "lib/index.css", "stylelint": {"extends": ["@vant/stylelint-config"]}, "typings": "types/index.d.ts", "version": "2.6.2", "vetur": {"tags": "vetur/tags.json", "attributes": "vetur/attributes.json"}, "web-types": "vetur/web-types.json"}