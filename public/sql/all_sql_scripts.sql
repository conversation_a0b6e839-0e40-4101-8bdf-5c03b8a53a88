-- 2025-06-06-01-01-用户表新增总金豆，已用金豆，当前金豆字段
alter table t_mini_account add golden decimal(16,2) COMMENT '总金豆';
alter table t_mini_account add used_golden decimal(16,2) COMMENT '已用金豆';
alter table t_mini_account add current_golden decimal(16,2) COMMENT '当前金豆';

-- 2025-06-05-02-03-购物车表新增会员类型id
alter table t_shopping_cart add member_type_id BIGINT COMMENT '会员类型id';

-- 2025-06-05-02-02-订单表新增省级编码，市级编码，区县编码，下单人用户id，现金金额，佣金支付金额，金豆支付金额
alter table t_order add  province_code VARCHAR(16) COMMENT '省级编码';
alter table t_order add  city_code VARCHAR(16) COMMENT '市级编码';
alter table t_order add  county_code VARCHAR(16) COMMENT '区/县编码';
alter table t_order add  order_user_id VARCHAR(20) COMMENT '下单人用户ID';
alter table t_order add  cash_amount decimal(16,2) COMMENT '现金金额';
alter table t_order add  commission_amount decimal(16,2) COMMENT '佣金支付金额';
alter table t_order add  golden_amount decimal(16,2) COMMENT '金豆支付金额';

-- 2025-06-05-02-01-用户地址表新增省级编码，市级编码，区县编码
alter table t_mini_account_address add  province_code VARCHAR(16) COMMENT '省级编码';
alter table t_mini_account_address add  city_code VARCHAR(16) COMMENT '市级编码';
alter table t_mini_account_address add  county_code VARCHAR(16) COMMENT '区/县编码';

-- 2025-06-05-01-01-新增提现方式特殊配置项
alter table t_special_setting add withdrawal_method TINYINT DEFAULT 1 COMMENT '提现方式:1->线下提现；2.线上提现；3.线上+线下提现';

-- 2025-06-04-01-04-特殊配置表新增商品显示会员价字段
alter table t_special_setting add show_member_price_flag TINYINT DEFAULT 0 COMMENT '商品显示会员价：0->关闭；1.开启';

-- 2025-06-04-01-03-小程序用户收藏表新增会员类型id
alter table t_mini_account_collect add member_type_id BIGINT COMMENT '会员类型id'; 

-- 2025-06-04-01-02-小程序用户足迹表新增会员类型id
alter table t_mini_account_foot_mark add member_type_id BIGINT COMMENT '会员类型id'; 

-- 2025-06-04-01-01-自定义页面表新增会员类型id
alter table t_shops_renovation_page add member_type_id BIGINT COMMENT '会员类型id';

-- 2025-06-03-01-03-用户信息扩展表新增状态字段
alter table t_mini_account_extends add status int COMMENT '状态：-1->冻结；0-正常';

-- 2025-06-03-01-02-新增激活会员商品表
SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;
DROP TABLE IF EXISTS `t_member_active_product`;
CREATE TABLE `t_member_active_product`  (
  `id` bigint NOT NULL COMMENT 'id',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `is_deleted` tinyint UNSIGNED NOT NULL DEFAULT 0 COMMENT '删除状态：0->未删除；1->已删除',
  `create_user_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人姓名',
  `create_user_id` bigint NULL DEFAULT NULL COMMENT '创建人id',
  `last_modify_user_id` bigint NULL DEFAULT NULL COMMENT '最近一次修改人id',
  `last_modify_user_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '最近一次修改人姓名',
  `tenant_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '租户id',
  `shop_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '店铺id',
  `product_id` BIGINT NULL  COMMENT '商品ID',
  `product_pic` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '商品图片',
  `product_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '商品名',
  `product_sku_id` BIGINT NULL  COMMENT '商品sku编号',
  `product_sku_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '商品sku条码',
  `product_quantity` INT NULL  COMMENT '商品数量',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '激活会员商品' ROW_FORMAT = DYNAMIC;
SET FOREIGN_KEY_CHECKS = 1; 